<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateRestables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tables', function (Blueprint $table) {
            if (!Schema::hasColumn('tables', 'x')) {
                $table->float('x')->nullable();
            }
            if (!Schema::hasColumn('tables', 'y')) {
                $table->float('y')->nullable();
            }
            if (!Schema::hasColumn('tables', 'w')) {
                $table->float('w')->nullable();
            }
            if (!Schema::hasColumn('tables', 'h')) {
                $table->float('h')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}

<?php

use Illuminate\Database\Migrations\Migration;

class RenameToCompany extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // No longer needed as restorants table is not created
        // All foreign keys now correctly reference 'companies' table
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Nothing to reverse as no rename was performed
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPriceToCouponsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('coupons', function (Blueprint $table) {
            if (!Schema::hasColumn('coupons', 'price')) {
                $table->decimal('price', 8, 2)->default(0)->after('discount');
            }
            if (!Schema::hasColumn('coupons', 'active_from')) {
                $table->datetime('active_from')->nullable()->after('valid_from');
            }
            if (!Schema::hasColumn('coupons', 'active_to')) {
                $table->datetime('active_to')->nullable()->after('valid_until');
            }
            if (!Schema::hasColumn('coupons', 'limit_to_num_uses')) {
                $table->integer('limit_to_num_uses')->default(0)->after('usage_limit');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('coupons', function (Blueprint $table) {
            if (Schema::hasColumn('coupons', 'price')) {
                $table->dropColumn('price');
            }
            if (Schema::hasColumn('coupons', 'active_from')) {
                $table->dropColumn('active_from');
            }
            if (Schema::hasColumn('coupons', 'active_to')) {
                $table->dropColumn('active_to');
            }
            if (Schema::hasColumn('coupons', 'limit_to_num_uses')) {
                $table->dropColumn('limit_to_num_uses');
            }
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class Kds extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            if (!Schema::hasColumn('orders', 'kds_finished')) {
                $table->integer('kds_finished')->default(0);
            }
        });

        if (Schema::hasTable('order_has_items')) {
            Schema::table('order_has_items', function (Blueprint $table) {
                if (!Schema::hasColumn('order_has_items', 'kds_finished')) {
                    $table->integer('kds_finished')->default(0);
                }
            });
        }

        if (Schema::hasTable('cart_storage')) {
            Schema::table('cart_storage', function (Blueprint $table) {
                if (!Schema::hasColumn('cart_storage', 'kds_finished')) {
                    $table->integer('kds_finished')->default(0);
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
